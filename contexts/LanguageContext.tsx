"use client";

import React, { createContext, useContext, useState, useEffect } from "react";
import { translations, type Language } from "../translations";

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
  dir: "ltr" | "rtl";
  isHydrated: boolean;
}

const LanguageContext = createContext<LanguageContextType | undefined>(
  undefined
);

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [language, setLanguageState] = useState<Language>("en");
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    // Mark as hydrated and load language from localStorage on mount
    setIsHydrated(true);

    if (typeof window !== "undefined") {
      const savedLanguage = localStorage.getItem("language") as Language;
      if (savedLanguage && (savedLanguage === "en" || savedLanguage === "ar")) {
        setLanguageState(savedLanguage);
      }
    }
  }, []);

  useEffect(() => {
    // Only update document attributes after hydration
    if (isHydrated && typeof window !== "undefined") {
      document.documentElement.lang = language;
      document.documentElement.dir = language === "ar" ? "rtl" : "ltr";
    }
  }, [language, isHydrated]);

  const setLanguage = (lang: Language) => {
    setLanguageState(lang);
    if (typeof window !== "undefined") {
      localStorage.setItem("language", lang);
    }
  };

  const t = (key: string): string => {
    const translation = translations[language][key as keyof (typeof translations)["en"]];
    
    if (translation) {
      return translation;
    }
    
    // Smart fallback: convert key to readable label
    // Example: "dashboard.card.inactiveTrips" -> "Inactive Trips"
    return convertKeyToLabel(key);
  };

  const convertKeyToLabel = (key: string): string => {
    // Split by dots and take the last part (most specific)
    const parts = key.split('.');
    const lastPart = parts[parts.length - 1];
    
    // Convert camelCase to space-separated words
    const words = lastPart
      .replace(/([A-Z])/g, ' $1') // Add space before capital letters
      .replace(/^./, str => str.toUpperCase()) // Capitalize first letter
      .trim();
    
    return words;
  };

  const dir = language === "ar" ? "rtl" : "ltr";

  return (
    <LanguageContext.Provider
      value={{ language, setLanguage, t, dir, isHydrated }}
    >
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error("useLanguage must be used within a LanguageProvider");
  }
  return context;
}
