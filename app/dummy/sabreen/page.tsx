"use client";

import { DataTable } from "@/components/shared/DataTable";
import MasterDetails from "@/components/MasterDetails";
import {
  BatteryIcon,
  CheckIcon,
  PowerOff,
} from "lucide-react";
import { useEffect, useState } from "react";

interface Trip {
  id: number;
  transitNumber: number;
  description: string;
  entry: string;
  lastSeen: string;
  tracker: string;
  driver: string;
  vehicle: string;
  alerts: string;
  status: ("active" | "charging" | "offline")[];
}

const statusConfig: Record<
  Trip["status"][number],
  {
    color: string;
    icon: React.ReactNode;
  }
> = {
  active: {
    color: "green",
    icon: <CheckIcon className="text-green-500" size={16} />,
  },
  charging: {
    color: "gray",
    icon: <BatteryIcon className="text-black" size={16} />,
  },
  offline: {
    color: "red",
    icon: <PowerOff className="text-red-500" size={16} />,
  },
};

export default function SabreenPage() {
  const [data, setData] = useState<Trip[]>([]);

  useEffect(() => {
    fetch("/data/trips.json")
      .then((res) => res.json())
      .then(setData)
      .catch(console.error);
  }, []);

  return (
    <div className="bg-gray-100 min-h-screen p-6 space-y-2 ">

      <DataTable<Trip>
        keyField="id"
        data={data}
        stickyHeader={true}
        expandable={true}
        expandRowByClick={false}
        exportable={true}
        exportFormats={["xlsx", "csv", "pdf"]}
        exportFileNameKey="trip-data"
        columns={[
          
          {
            key: "transitNumber",
            title: "Transit Number",
            render: (row) => (
              <span className="text-blue-400 font-medium">
                {row.transitNumber}
              </span>
            ),
          },
          { key: "description", title: "Shipment Description" },
          { key: "entry", title: "Entry-port - Exit-port" },
          { key: "lastSeen", title: "Last Seen" },
          { key: "tracker", title: "Tracker" },
          { key: "driver", title: "Driver Name" },
          { key: "vehicle", title: "Vehicle" },
          { key: "alerts", title: "Alerts" },
          {
            key: "status",
            title: "Trip Status",
            render: (row) => (
              <div className="flex gap-1 items-center">
                {row.status?.map((s, idx) => (
                  <span key={idx}>{statusConfig[s]?.icon}</span>
                ))}
              </div>
            ),
          },
        ]}
        expandedRowRender={(row) => <MasterDetails row={row} />}
      />
    </div>
  );
}
