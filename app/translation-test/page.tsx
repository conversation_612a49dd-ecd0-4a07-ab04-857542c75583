"use client";

import { useLanguage } from '../../contexts/LanguageContext';
import PageTemplate from '../../components/layout/PageTemplate';

export default function TranslationTestPage() {
  const { t } = useLanguage();

  // Test cases for smart translation fallback
  const testKeys = [
    'dashboard.card.inactiveTrips', // Should become "Inactive Trips"
    'user.profile.personalInfo', // Should become "Personal Info"
    'navigation.myAssignedPorts', // Exists in translation, should use actual translation
    'settings.account.changePassword', // Should become "Change Password"
    'reports.analytics.monthlyData', // Should become "Monthly Data"
    'system.notifications.emailAlerts', // Should become "Email Alerts"
    'vehicle.maintenance.scheduledService', // Should become "Scheduled Service"
    'trip.status.inProgress', // Should become "In Progress"
    'alerts.priority.highRisk', // Should become "High Risk"
    'configuration.general.defaultSettings' // Should become "Default Settings"
  ];

  return (
    <PageTemplate titleKey="translation.test.title">
      <div className="container mx-auto px-4 py-8">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">
            Translation Smart Fallback Test
          </h1>
          
          <div className="mb-6">
            <p className="text-gray-600 mb-4">
              This page demonstrates the smart translation fallback system. 
              When a translation key is not found, it automatically converts 
              the key to a readable label.
            </p>
          </div>

          <div className="grid gap-4">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">
              Test Cases:
            </h2>
            
            {testKeys.map((key, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <span className="text-sm font-medium text-gray-500">Key:</span>
                    <div className="font-mono text-sm bg-gray-100 p-2 rounded">
                      {key}
                    </div>
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-500">Result:</span>
                    <div className="text-lg font-semibold text-blue-600">
                      {t(key)}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-8 p-4 bg-blue-50 rounded-lg">
            <h3 className="text-lg font-semibold text-blue-800 mb-2">
              How it works:
            </h3>
            <ul className="text-blue-700 space-y-1">
              <li>• Takes the last part of the key after the final dot</li>
              <li>• Converts camelCase to space-separated words</li>
              <li>• Capitalizes the first letter</li>
              <li>• Falls back to this readable format when translation is missing</li>
              <li>• Uses actual translation when available</li>
            </ul>
          </div>
        </div>
      </div>
    </PageTemplate>
  );
}