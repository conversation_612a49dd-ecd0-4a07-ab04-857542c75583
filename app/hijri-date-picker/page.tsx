"use client";

import React, { useState, useEffect } from "react";
import DateTimePicker from "@/components/shared/DateTimePicker";
import { useLanguage } from "@/contexts/LanguageContext";

// Import the HijriDate class from the DateTimePicker component
// For demo purposes, we'll create a simple version here
class HijriDate {
  static hijriMonthsAr = [
    "محرم",
    "صفر",
    "ربيع الأول",
    "ربيع الثاني",
    "جمادى الأولى",
    "جمادى الثانية",
    "رجب",
    "شعبان",
    "رمضان",
    "شوال",
    "ذو القعدة",
    "ذو الحجة",
  ];

  static toHijri(gregorianDate: Date) {
    // Simplified conversion for demo
    const jd = this.gregorian<PERSON>o<PERSON><PERSON>an(gregorianDate);
    const hijri = this.julian<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(jd);

    return {
      year: hijri.year,
      month: hijri.month,
      day: hijri.day,
      monthName: this.hijriMonthsAr[hijri.month - 1],
    };
  }

  private static gregorianToJ<PERSON>an(date: Date): number {
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();

    const a = Math.floor((14 - month) / 12);
    const y = year - a;
    const m = month + 12 * a - 3;

    return (
      day +
      Math.floor((153 * m + 2) / 5) +
      365 * y +
      Math.floor(y / 4) -
      Math.floor(y / 100) +
      Math.floor(y / 400) +
      1721119
    );
  }

  private static julianToHijri(jd: number): {
    year: number;
    month: number;
    day: number;
  } {
    const epochJd = 1948439.5;
    const daysSinceEpoch = jd - epochJd;

    const lunarMonth = 29.530588;
    const lunarYear = 12 * lunarMonth;

    let year = Math.floor(daysSinceEpoch / lunarYear) + 1;
    let remainingDays = daysSinceEpoch - (year - 1) * lunarYear;

    let month = Math.floor(remainingDays / lunarMonth) + 1;
    if (month > 12) {
      year++;
      month = 1;
      remainingDays -= 12 * lunarMonth;
    }

    let day = Math.floor(remainingDays - (month - 1) * lunarMonth) + 1;

    year = Math.max(1, year);
    month = Math.max(1, Math.min(12, month));
    day = Math.max(1, Math.min(30, day));

    return { year, month, day };
  }
}

export default function HijriDatePickerDemo() {
  const { language } = useLanguage();
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [selectedDate2, setSelectedDate2] = useState<Date>();
  const [selectedDate3, setSelectedDate3] = useState<Date>();
  const [todayHijri, setTodayHijri] = useState<string>("");

  useEffect(() => {
    const today = new Date();
    const hijriDate = HijriDate.toHijri(today);
    const hijriString = `${hijriDate.day} ${hijriDate.monthName} ${hijriDate.year} هـ`;
    setTodayHijri(hijriString);
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-3xl font-bold text-center mb-8 text-gray-800">
            {language === "ar"
              ? "منتقي التاريخ الهجري"
              : "Hijri Date Picker Demo"}
          </h1>

          <div className="space-y-8">
            {/* Today's Date Display */}
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-lg border border-blue-200">
              <h2 className="text-xl font-semibold mb-4 text-blue-800">
                {language === "ar" ? "التاريخ اليوم" : "Today's Date"}
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <h3 className="font-medium text-gray-700 mb-2">
                    {language === "ar" ? "التاريخ الميلادي" : "Gregorian Date"}
                  </h3>
                  <p className="text-lg text-gray-900">
                    {new Date().toLocaleDateString(
                      language === "ar" ? "ar-SA" : "en-US",
                      {
                        year: "numeric",
                        month: "long",
                        day: "numeric",
                        weekday: "long",
                      }
                    )}
                  </p>
                </div>
                <div className="bg-white p-4 rounded-lg shadow-sm">
                  <h3 className="font-medium text-gray-700 mb-2">
                    {language === "ar" ? "التاريخ الهجري" : "Hijri Date"}
                  </h3>
                  <p
                    className="text-lg text-gray-900"
                    dir={language === "ar" ? "rtl" : "ltr"}
                  >
                    {todayHijri || "٥ صفر ١٤٤٧ هـ"}
                  </p>
                </div>
              </div>
            </div>

            {/* Demo Examples */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Basic Hijri Date Picker */}
              <div className="space-y-4">
                <h2 className="text-xl font-semibold text-gray-800">
                  {language === "ar"
                    ? "منتقي التاريخ الهجري الأساسي"
                    : "Basic Hijri Date Picker"}
                </h2>
                <DateTimePicker
                  label={language === "ar" ? "التاريخ الهجري" : "Hijri Date"}
                  value={selectedDate}
                  onChange={setSelectedDate}
                  dateType="hijri"
                  format="full"
                  placeholder={
                    language === "ar"
                      ? "اختر التاريخ الهجري"
                      : "Select Hijri date"
                  }
                />
                {selectedDate && (
                  <div className="mt-2 p-3 bg-green-50 border border-green-200 rounded-md">
                    <p className="text-sm text-green-800">
                      <strong>
                        {language === "ar"
                          ? "التاريخ المحدد:"
                          : "Selected Date:"}
                      </strong>{" "}
                      {selectedDate.toLocaleDateString()}
                    </p>
                  </div>
                )}
              </div>

              {/* Hijri Date Picker with Time */}
              <div className="space-y-4">
                <h2 className="text-xl font-semibold text-gray-800">
                  {language === "ar"
                    ? "منتقي التاريخ والوقت الهجري"
                    : "Hijri Date & Time Picker"}
                </h2>
                <DateTimePicker
                  label={
                    language === "ar"
                      ? "التاريخ والوقت الهجري"
                      : "Hijri Date & Time"
                  }
                  value={selectedDate2}
                  onChange={setSelectedDate2}
                  dateType="hijri"
                  format="full"
                  showTime={true}
                  timeFormat="12h"
                  placeholder={
                    language === "ar"
                      ? "اختر التاريخ والوقت"
                      : "Select date and time"
                  }
                />
                {selectedDate2 && (
                  <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-md">
                    <p className="text-sm text-blue-800">
                      <strong>
                        {language === "ar"
                          ? "التاريخ والوقت المحدد:"
                          : "Selected Date & Time:"}
                      </strong>{" "}
                      {selectedDate2.toLocaleString()}
                    </p>
                  </div>
                )}
              </div>

              {/* Switchable Calendar Type */}
              <div className="space-y-4">
                <h2 className="text-xl font-semibold text-gray-800">
                  {language === "ar"
                    ? "منتقي التاريخ القابل للتبديل"
                    : "Switchable Calendar Type"}
                </h2>
                <DateTimePicker
                  label={
                    language === "ar"
                      ? "التاريخ (هجري/ميلادي)"
                      : "Date (Hijri/Gregorian)"
                  }
                  value={selectedDate3}
                  onChange={setSelectedDate3}
                  dateType="hijri"
                  format="full"
                  allowTypeSwitch={true}
                  placeholder={
                    language === "ar" ? "اختر التاريخ" : "Select date"
                  }
                />
                {selectedDate3 && (
                  <div className="mt-2 p-3 bg-purple-50 border border-purple-200 rounded-md">
                    <p className="text-sm text-purple-800">
                      <strong>
                        {language === "ar"
                          ? "التاريخ المحدد:"
                          : "Selected Date:"}
                      </strong>{" "}
                      {selectedDate3.toLocaleDateString()}
                    </p>
                  </div>
                )}
              </div>

              {/* Different Formats */}
              <div className="space-y-4">
                <h2 className="text-xl font-semibold text-gray-800">
                  {language === "ar" ? "تنسيقات مختلفة" : "Different Formats"}
                </h2>
                <div className="space-y-3">
                  <DateTimePicker
                    label={language === "ar" ? "تنسيق قصير" : "Short Format"}
                    dateType="hijri"
                    format="short"
                    placeholder={
                      language === "ar" ? "تنسيق قصير" : "Short format"
                    }
                  />
                  <DateTimePicker
                    label={
                      language === "ar"
                        ? "تنسيق DD/MM/YYYY"
                        : "DD/MM/YYYY Format"
                    }
                    dateType="hijri"
                    format="dd/mm/yyyy"
                    placeholder="DD/MM/YYYY"
                  />
                </div>
              </div>
            </div>

            {/* Features List */}
            <div className="bg-gray-50 p-6 rounded-lg">
              <h2 className="text-xl font-semibold mb-4 text-gray-800">
                {language === "ar" ? "الميزات" : "Features"}
              </h2>
              <ul className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-700">
                <li className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  <span>
                    {language === "ar"
                      ? "تحويل دقيق للتاريخ الهجري"
                      : "Accurate Hijri date conversion"}
                  </span>
                </li>
                <li className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  <span>
                    {language === "ar"
                      ? "أسماء الأشهر الهجرية بالعربية"
                      : "Arabic Hijri month names"}
                  </span>
                </li>
                <li className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  <span>
                    {language === "ar"
                      ? "دعم اللغتين العربية والإنجليزية"
                      : "Bilingual support (Arabic/English)"}
                  </span>
                </li>
                <li className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  <span>
                    {language === "ar"
                      ? "تصميم عصري ومتجاوب"
                      : "Modern responsive design"}
                  </span>
                </li>
                <li className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  <span>
                    {language === "ar"
                      ? "اختيار الوقت (12/24 ساعة)"
                      : "Time selection (12/24 hour)"}
                  </span>
                </li>
                <li className="flex items-center space-x-2">
                  <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                  <span>
                    {language === "ar"
                      ? "تبديل بين الهجري والميلادي"
                      : "Switch between Hijri/Gregorian"}
                  </span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
