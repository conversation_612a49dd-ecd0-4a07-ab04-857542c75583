# DateTimePicker Component Documentation

## Overview

The DateTimePicker is a comprehensive React component that supports both Gregorian and Hijri calendar systems with full date and time selection capabilities. It features bilingual support (Arabic/English), multiple date formats, and a user-friendly interface with calendar navigation.

## ✨ Key Features

### 🗓️ **Dual Calendar Support**

- **Gregorian Calendar**: Standard international calendar
- **Hijri Calendar**: Islamic lunar calendar with accurate month calculations
- **Calendar Switching**: Toggle between calendar types with a single click

### ⏰ **Time Selection**

- **Optional Time Picker**: Enable/disable time selection
- **Multiple Time Formats**: 12-hour (AM/PM) and 24-hour formats
- **Time Preservation**: Maintains selected time when switching dates

### 🌍 **Internationalization**

- **Bilingual Support**: Arabic and English languages
- **Localized Formatting**: Date and time formats adapt to language
- **RTL Support**: Right-to-left layout for Arabic

### 📅 **Date Formats**

- `dd/mm/yyyy` - Day/Month/Year
- `mm/dd/yyyy` - Month/Day/Year (US format)
- `yyyy-mm-dd` - ISO format
- `dd-mm-yyyy` - Day-Month-Year with dashes
- `short` - Compact format (e.g., "15/1/1428 هـ")
- `full` - Full format with month names (e.g., "15 محرم 1428 هـ")

### 🎯 **User Experience**

- **Click Outside to Close**: Calendar closes when clicking outside
- **Close Button**: Manual close option with X button
- **Keyboard Navigation**: Arrow keys for month/year navigation
- **Visual Feedback**: Hover effects and selection highlighting
- **Today Indicator**: Current date highlighted with border

## 🔧 Recent Fixes & Improvements

### ✅ **Fixed Hijri Calendar Issues**

- **Accurate Day Selection**: Fixed issue where clicking any day selected all days
- **Correct Day Display**: Resolved "6 محرم only" bug - now shows exact selected day
- **Proper Month Calculations**: Implemented correct 29/30 day alternating pattern
- **Leap Year Support**: Added 30-year Hijri leap year cycle calculations

### ✅ **Enhanced Date Handling**

- **No Third-Party Dependencies**: Removed chrono-node, using native JavaScript
- **Improved Date Parsing**: Custom parseInputDate function for common formats
- **State Management**: Centralized input value updates via useEffect
- **Date Object Integrity**: Proper date object creation to avoid reference issues

### ✅ **Technical Improvements**

- **Direct Hijri Storage**: Stores original Hijri date info to avoid conversion precision loss
- **Smart Date Formatting**: Uses stored Hijri info when available, falls back to conversion
- **Centralized State Management**: Single useEffect handles all input value updates
- **Memory Optimization**: Proper cleanup and reference management

## 📋 Component Interface

### Props

```typescript
interface DateTimePickerProps {
  value?: Date; // Initial/controlled date value
  onChange?: (date: Date) => void; // Callback when date changes
  format?: DateFormat; // Date display format
  dateType?: DateType; // 'gregorian' | 'hijri'
  showTime?: boolean; // Enable time selection
  timeFormat?: "12h" | "24h"; // Time display format
  placeholder?: string; // Input placeholder text
  allowTypeSwitch?: boolean; // Allow calendar type switching
  className?: string; // Additional CSS classes
  disabled?: boolean; // Disable the component
}
```

### Date Format Options

- `'dd/mm/yyyy'` - 15/01/1428
- `'mm/dd/yyyy'` - 01/15/1428
- `'yyyy-mm-dd'` - 1428-01-15
- `'dd-mm-yyyy'` - 15-01-1428
- `'short'` - 15/1/1428 هـ
- `'full'` - 15 محرم 1428 هـ

## Installation & Setup

### 1. Prerequisites

```typescript
// Required imports in your project
import React from "react";
import { CalendarIcon, ChevronLeft, ChevronRight, X } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";
```

### 2. Language Context

The component requires a language context for internationalization:

```typescript
// contexts/LanguageContext.tsx
interface LanguageContextType {
  language: "ar" | "en";
  setLanguage: (lang: "ar" | "en") => void;
}

export const useLanguage = () => {
  // Your language context implementation
};
```

## 💡 Usage Examples

### Basic Date Picker

```typescript
import { DateTimePicker } from "@/components/shared/DateTimePicker";

function MyComponent() {
  const [selectedDate, setSelectedDate] = useState<Date>();

  return (
    <DateTimePicker
      value={selectedDate}
      onChange={setSelectedDate}
      placeholder="Select a date"
    />
  );
}
```

### Hijri Calendar with Time

```typescript
<DateTimePicker
  value={selectedDate}
  onChange={setSelectedDate}
  dateType="hijri"
  format="full"
  showTime={true}
  timeFormat="12h"
  placeholder="اختر التاريخ والوقت"
/>
```

### Different Date Formats

```typescript
// Gregorian with ISO format
<DateTimePicker
  dateType="gregorian"
  format="yyyy-mm-dd"
  placeholder="YYYY-MM-DD"
/>

// Hijri with short format
<DateTimePicker
  dateType="hijri"
  format="short"
  placeholder="التاريخ الهجري"
/>
```

## 🔧 Technical Implementation

### 1. Enhanced Hijri Date Handling

The component uses a sophisticated approach to handle Hijri dates accurately:

```typescript
class HijriDate {
  // Convert Gregorian date to Hijri with accurate calculations
  static toHijri(gregorianDate: Date): {
    year: number;
    month: number;
    day: number;
    monthName: string;
  };

  // Convert Hijri date back to Gregorian Date object
  static fromHijri(
    hijriYear: number,
    hijriMonth: number,
    hijriDay: number
  ): Date;

  // Get correct number of days (29 or 30) for Hijri month
  static getHijriMonthDays(year: number, month: number): number;

  // Check if Hijri year is a leap year (30-year cycle)
  static isHijriLeapYear(year: number): boolean;
}
```

### 2. Direct Hijri Date Storage

To avoid precision loss from round-trip conversions, the component stores original Hijri date information:

```typescript
const [selectedHijriDate, setSelectedHijriDate] = useState<{
  year: number;
  month: number;
  day: number;
  monthName: string;
} | null>(null);

// When selecting from Hijri calendar, store the exact Hijri info
const handleDateSelect = (date: Date, hijriInfo?: HijriInfo) => {
  if (hijriInfo) {
    setSelectedHijriDate(hijriInfo); // Preserves exact day clicked
  }
  setSelectedDate(date);
};
```

### 3. Smart Date Formatting

The enhanced `formatDateByType` function uses stored Hijri info when available:

```typescript
function formatDateByType(
  date: Date | undefined,
  dateType: DateType,
  format: DateFormat,
  language: string,
  showTime: boolean = false,
  timeFormat: "12h" | "24h" = "24h",
  hijriInfo?: HijriInfo | null // NEW: Use stored Hijri info
): string {
  if (dateType === "hijri") {
    // Use provided hijriInfo if available, otherwise convert from Gregorian
    const hijri = hijriInfo || HijriDate.toHijri(date);

    // This ensures exact day preservation (e.g., day 15 stays day 15)
    switch (format) {
      case "full":
        return language === "ar"
          ? `${hijri.day} ${hijri.monthName} ${hijri.year} هـ`
          : `${hijri.day} ${hijri.monthName} ${hijri.year} AH`;
      // ... other formats
    }
  }
}
```

### 4. Native Date Parsing

Custom date parsing without external dependencies:

```typescript
const parseInputDate = (input: string): Date | null => {
  // Supports multiple formats:
  // - ISO: 2024-01-15
  // - DD/MM/YYYY: 15/01/2024
  // - MM/DD/YYYY: 01/15/2024
  // - DD-MM-YYYY: 15-01-2024
  // - YYYY/MM/DD: 2024/01/15
};
```

## Usage Examples

### Basic Date Picker

```tsx
import DateTimePicker from "@/components/shared/DateTimePicker";

function MyComponent() {
  const [selectedDate, setSelectedDate] = useState<Date>();

  return (
    <DateTimePicker
      label="Select Date"
      dateType="gregorian"
      format="dd/mm/yyyy"
      value={selectedDate}
      onChange={setSelectedDate}
    />
  );
}
```

### Hijri Calendar

```tsx
<DateTimePicker
  label="التاريخ الهجري"
  dateType="hijri"
  format="full"
  allowTypeSwitch={true}
  placeholder="اختر التاريخ"
  onChange={(date) => console.log("Hijri date:", date)}
/>
```

### Date & Time Picker

```tsx
<DateTimePicker
  label="Date & Time"
  dateType="gregorian"
  format="full"
  showTime={true}
  timeFormat="24h"
  allowTypeSwitch={false}
  onChange={(date) => console.log("DateTime:", date)}
/>
```

### 12-Hour Time Format

```tsx
<DateTimePicker
  label="Appointment Time"
  dateType="gregorian"
  format="short"
  showTime={true}
  timeFormat="12h"
  onChange={(date) => console.log("12h format:", date)}
/>
```

## Advanced Features

### Calendar Type Switching

When `allowTypeSwitch={true}`, users can toggle between Hijri and Gregorian calendars:

```tsx
<DateTimePicker
  label="Flexible Date"
  dateType="gregorian"
  format="full"
  allowTypeSwitch={true}
  onChange={(date) => console.log("Switched date:", date)}
/>
```

### Disabled State

```tsx
<DateTimePicker
  label="Read-only Date"
  dateType="gregorian"
  format="full"
  disabled={true}
  value={new Date()}
/>
```

### Pre-selected Date with Time

```tsx
<DateTimePicker
  label="Meeting Schedule"
  dateType="gregorian"
  format="full"
  showTime={true}
  timeFormat="12h"
  value={new Date(2024, 0, 15, 14, 30)} // Jan 15, 2024, 2:30 PM
  onChange={(date) => console.log("Meeting:", date)}
/>
```

## Styling & Customization

### CSS Classes

The component uses Tailwind CSS classes. Key styling points:

```css
/* Main container */
.flex.flex-col.gap-2

.w-full.px-3.py-2.border.border-gray-300.rounded-md

/* Calendar popup */
.absolute.top-full.left-0.mt-1.bg-white.border.shadow-lg.z-50

/* Time picker */
.p-4.border-t.bg-gray-50

/* Selected date */
.bg-blue-500.text-white

/* Today's date */
.border.border-blue-500;
```

### Custom Styling

```tsx
<DateTimePicker
  className="my-custom-picker"
  label="Custom Styled Picker"
  dateType="gregorian"
  format="dd/mm/yyyy"
/>
```

## Implementation Steps

### Step 1: Create the Component Structure

```typescript
export default function DateTimePicker({
  value,
  onChange,
  label,
  placeholder,
  dateType = "gregorian",
  format = "dd/mm/yyyy",
  showTime = false,
  timeFormat = "24h",
  allowTypeSwitch = true,
  className = "",
  disabled = false,
}: DateTimePickerProps) {
  // Component implementation
}
```

### Step 2: Set Up State Management

```typescript
const { language } = useLanguage();
const [open, setOpen] = useState(false);
const [inputValue, setInputValue] = useState("");
const [selectedDate, setSelectedDate] = useState<Date | undefined>(value);
const [currentDateType, setCurrentDateType] = useState<DateType>(dateType);
const [currentMonth, setCurrentMonth] = useState(new Date());
const pickerRef = useRef<HTMLDivElement>(null);
```

### Step 3: Implement Date Formatting

```typescript
function formatDateByType(
  date: Date | undefined,
  dateType: DateType,
  format: DateFormat,
  language: string,
  showTime: boolean = false,
  timeFormat: "12h" | "24h" = "24h"
): string {
  if (!date) return "";

  const formatTime = (date: Date): string => {
    if (timeFormat === "12h") {
      return date.toLocaleTimeString(language === "ar" ? "ar-SA" : "en-US", {
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      });
    } else {
      return `${date.getHours().toString().padStart(2, "0")}:${date
        .getMinutes()
        .toString()
        .padStart(2, "0")}`;
    }
  };

  // Format date based on type and format
  let dateStr = "";

  if (dateType === "hijri") {
    const hijriDate = HijriDate.toHijri(date);
    // Format Hijri date
  } else {
    // Format Gregorian date
  }

  return showTime ? `${dateStr} ${formatTime(date)}` : dateStr;
}
```

### Step 4: Implement Calendar Rendering

```typescript
const renderCalendar = () => {
  const today = new Date();

  if (currentDateType === "hijri") {
    // Hijri calendar rendering
    const currentHijri = HijriDate.toHijri(currentMonth);
    const hijriMonths = [
      "محرم",
      "صفر",
      "ربيع الأول",
      "ربيع الثاني",
      "جمادى الأولى",
      "جمادى الثانية",
      "رجب",
      "شعبان",
      "رمضان",
      "شوال",
      "ذو القعدة",
      "ذو الحجة",
    ];

    // Render Hijri calendar with proper month navigation
    return (
      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <button onClick={handlePrevMonth}>
            <ChevronLeft className="w-4 h-4" />
          </button>
          <span className="font-medium">
            {hijriMonths[currentHijri.month - 1]} {currentHijri.year}
          </span>
          <button onClick={handleNextMonth}>
            <ChevronRight className="w-4 h-4" />
          </button>
        </div>
        {/* Calendar grid */}
      </div>
    );
  } else {
    // Gregorian calendar rendering
    // Similar structure but with Gregorian dates
  }
};
```

### Step 5: Add Event Handlers

```typescript
// Handle input changes with native date parsing
const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  const newValue = e.target.value;
  setInputValue(newValue);

  const parsedDate = parseInputDate(newValue);
  if (parsedDate) {
    setSelectedDate(parsedDate);
    onChange?.(parsedDate);
  }
};

// Handle date selection from calendar
const handleDateSelect = (date: Date) => {
  // Preserve time if showTime is enabled
  if (showTime && selectedDate) {
    date.setHours(selectedDate.getHours());
    date.setMinutes(selectedDate.getMinutes());
    date.setSeconds(selectedDate.getSeconds());
  }

  setSelectedDate(date);
  setInputValue(
    formatDateByType(
      date,
      currentDateType,
      format,
      language,
      showTime,
      timeFormat
    )
  );
  onChange?.(date);

  if (!showTime) {
    setOpen(false);
  }
};

// Handle outside click to close
useEffect(() => {
  const handleClickOutside = (event: MouseEvent) => {
    if (
      pickerRef.current &&
      !pickerRef.current.contains(event.target as Node)
    ) {
      setOpen(false);
    }
  };

  if (open) {
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }
}, [open]);
```

### Step 6: Render the Component

```typescript
return (
  <div className={`flex flex-col gap-2 ${className}`}>
    {label && (
      <label className="text-sm font-medium text-gray-700">{label}</label>
    )}

    <div className="relative">
      {/* Input field */}
      <input
        type="text"
        value={inputValue}
        onChange={handleInputChange}
        placeholder={placeholder}
        disabled={disabled}
        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
      />

      {/* Calendar toggle button */}
      <button
        type="button"
        onClick={() => !disabled && setOpen(!open)}
        disabled={disabled}
        className="absolute right-2 top-1/2 -translate-y-1/2 p-1 hover:bg-gray-100 rounded"
      >
        <CalendarIcon className="w-4 h-4 text-gray-500" />
      </button>

      {/* Calendar popup */}
      {open && (
        <div
          ref={pickerRef}
          className="absolute top-full left-0 mt-1 bg-white border shadow-lg z-50"
        >
          {/* Calendar header with close button */}
          <div className="p-3 border-b bg-gray-50">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">
                {/* Calendar type label */}
              </span>
              <div className="flex items-center gap-2">
                {/* Type switch button */}
                {/* Close button */}
                <button onClick={() => setOpen(false)}>
                  <X className="w-4 h-4 text-gray-500" />
                </button>
              </div>
            </div>
          </div>

          {/* Calendar grid */}
          {renderCalendar()}

          {/* Time picker (if enabled) */}
          {showTime && (
            <div className="p-4 border-t bg-gray-50">
              {/* Time input fields */}
            </div>
          )}

          {/* Selected date display */}
          {selectedDate && (
            <div className="p-3 border-t bg-gray-50 text-center text-sm text-gray-600">
              {/* Show both Gregorian and Hijri dates */}
            </div>
          )}
        </div>
      )}
    </div>

    {/* Current selection display */}
    {selectedDate && (
      <div className="text-sm text-gray-600">
        {language === "ar" ? "التاريخ المحدد: " : "Selected: "}
        <span className="font-medium">
          {formatDateByType(
            selectedDate,
            currentDateType,
            format,
            language,
            showTime,
            timeFormat
          )}
        </span>
      </div>
    )}
  </div>
);
```

## Key Implementation Details

### 1. Hijri Calendar Logic

The Hijri calendar implementation uses astronomical calculations:

```typescript
// Convert Gregorian to Hijri
static toHijri(gregorianDate: Date) {
  // Julian Day Number calculation
  const jdn = /* complex calculation */;

  // Convert to Hijri
  const hijriJdn = jdn - 1948440;
  const hijriYear = Math.floor((30 * hijriJdn + 10646) / 10631);
  const hijriMonth = /* month calculation */;
  const hijriDay = /* day calculation */;

  return { year: hijriYear, month: hijriMonth, day: hijriDay };
}
```

### 2. Time Picker Implementation

```typescript
// Time input with 2-digit formatting
<input
  type="text"
  value={
    selectedDate ? selectedDate.getHours().toString().padStart(2, "0") : "00"
  }
  onChange={(e) => {
    if (selectedDate) {
      const newDate = new Date(selectedDate);
      newDate.setHours(parseInt(e.target.value) || 0);
      setSelectedDate(newDate);
      onChange?.(newDate);
    }
  }}
  onBlur={(e) => {
    // Ensure 2-digit format and validate range
    const value = parseInt(e.target.value) || 0;
    const clampedValue = Math.min(Math.max(value, 0), 23);
    e.target.value = clampedValue.toString().padStart(2, "0");
  }}
/>
```

### 3. Native Date Parsing

```typescript
const parseInputDate = (input: string): Date | null => {
  if (!input.trim()) return null;

  // Try ISO format first
  const isoDate = new Date(input);
  if (!isNaN(isoDate.getTime())) return isoDate;

  // Try common formats with regex
  const formats = [
    /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/, // dd/mm/yyyy
    /^(\d{4})-(\d{1,2})-(\d{1,2})$/, // yyyy-mm-dd
    // ... more formats
  ];

  for (const format of formats) {
    const match = input.match(format);
    if (match) {
      // Parse and validate date parts
      const date = new Date(year, month - 1, day);
      if (!isNaN(date.getTime())) return date;
    }
  }

  return null;
};
```

## Testing & Validation

### Test Cases

1. **Basic Functionality**

   - Date selection from calendar
   - Input field date entry
   - Format switching

2. **Hijri Calendar**

   - Month navigation
   - Proper Arabic month names
   - Date conversion accuracy

3. **Time Picker**

   - 12h/24h format switching
   - Time validation
   - AM/PM toggle

4. **Edge Cases**
   - Invalid date inputs
   - Leap years
   - Month boundaries
   - Time boundaries (23:59 → 00:00)

### Example Test Page

```tsx
// Test different configurations
const testCases = [
  { label: "Basic Gregorian", dateType: "gregorian", format: "dd/mm/yyyy" },
  {
    label: "Hijri with Time",
    dateType: "hijri",
    showTime: true,
    timeFormat: "24h",
  },
  {
    label: "12h Format",
    dateType: "gregorian",
    showTime: true,
    timeFormat: "12h",
  },
  { label: "Disabled", disabled: true, value: new Date() },
];

return (
  <div className="space-y-4">
    {testCases.map((config, index) => (
      <DateTimePicker
        key={index}
        {...config}
        onChange={(date) => console.log(`${config.label}:`, date)}
      />
    ))}
  </div>
);
```

## Performance Considerations

1. **Memoization**: Use `React.memo` for expensive calculations
2. **Event Listeners**: Proper cleanup of outside click listeners
3. **Date Calculations**: Cache Hijri conversions when possible
4. **Re-renders**: Minimize unnecessary re-renders with proper dependencies

## Browser Compatibility

- ✅ Modern browsers (Chrome, Firefox, Safari, Edge)
- ✅ Mobile browsers (iOS Safari, Chrome Mobile)
- ✅ RTL language support
- ⚠️ IE11 requires polyfills for modern JavaScript features

## Troubleshooting

### Common Issues

1. **Date not parsing**: Check input format matches expected patterns
2. **Hijri dates incorrect**: Verify HijriDate calculations
3. **Time not updating**: Ensure proper state management
4. **Calendar not closing**: Check outside click event handling
5. **Styling issues**: Verify Tailwind CSS classes are available

### Debug Tips

```typescript
// Add debug logging
console.log("Parsed date:", parseInputDate(inputValue));
console.log("Hijri conversion:", HijriDate.toHijri(new Date()));
console.log("Formatted output:", formatDateByType(date, type, format, lang));
```

## 🐛 Recent Bug Fixes & Solutions

### ✅ **Fixed: Hijri Calendar Day Selection Issue**

**Problem**: Clicking any day in Hijri calendar selected all days instead of just the clicked day.

**Solution**:

- Improved date comparison logic for Hijri dates
- Added proper date object creation with time reset
- Enhanced selection state management

### ✅ **Fixed: Date Submission Issue**

**Problem**: Selected dates weren't being submitted to the input field.

**Solution**:

- Removed duplicate `setInputValue` calls causing race conditions
- Centralized input value updates through single `useEffect`
- Added proper initial value handling

### ✅ **Fixed: "6 محرم Only" Display Bug**

**Problem**: Hijri calendar always showed "6 محرم" regardless of selected day.

**Solution**:

- Implemented direct Hijri date storage to avoid conversion precision loss
- Enhanced `formatDateByType` to use stored Hijri info when available
- Eliminated round-trip conversion issues

### ✅ **Fixed: Hijri Month Day Calculations**

**Problem**: All Hijri months showed 30 days instead of proper 29/30 alternating pattern.

**Solution**:

- Added `getHijriMonthDays()` method with proper alternating logic
- Implemented 30-year Hijri leap year cycle calculations
- Fixed calendar rendering to show correct number of days

## 🔍 Troubleshooting Guide

### Common Issues & Solutions

| Issue                      | Cause                                | Solution                              |
| -------------------------- | ------------------------------------ | ------------------------------------- |
| **Wrong day displayed**    | Round-trip conversion precision loss | Use stored `hijriInfo` parameter      |
| **Calendar doesn't close** | Missing outside click handler        | Check `pickerRef` and event listeners |
| **Time not preserved**     | State update order                   | Ensure time is set before date update |
| **Styling broken**         | Missing Tailwind classes             | Verify CSS framework is loaded        |
| **Date parsing fails**     | Unsupported format                   | Check `parseInputDate` regex patterns |

### Debug Checklist

```typescript
// 1. Check date conversion accuracy
const testDate = new Date(2024, 0, 15); // Jan 15, 2024
const hijri = HijriDate.toHijri(testDate);
const backToGregorian = HijriDate.fromHijri(hijri.year, hijri.month, hijri.day);
console.log("Original:", testDate, "Hijri:", hijri, "Back:", backToGregorian);

// 2. Verify state updates
useEffect(() => {
  console.log("Selected date changed:", selectedDate);
  console.log("Selected Hijri info:", selectedHijriDate);
}, [selectedDate, selectedHijriDate]);

// 3. Test date formatting
const formatted = formatDateByType(
  date,
  "hijri",
  "full",
  "ar",
  false,
  "24h",
  hijriInfo
);
console.log("Formatted result:", formatted);
```

This comprehensive implementation provides a fully-featured DateTimePicker component with dual calendar support, time selection, and robust error handling - all without external dependencies.
