"use client";

import {
  <PERSON>,
  TableBody,
  Table<PERSON>ell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { cn } from "@/lib/utils";
import { useState } from "react";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import jsPDF from "jspdf";
import autoTable from "jspdf-autotable";
import { AlertCircleIcon } from "lucide-react";

// Interfaces
interface Column<T> {
  key: string;
  title: string;
  render?: (row: T) => React.ReactNode;
  width?: string;
  align?: "left" | "center" | "right";
  sticky?: boolean;
}

interface Action<T> {
  icon: React.ReactNode;
  abelKey: string;
  onClick: (row: T) => void;
  variant?: "primary" | "secondary" | "danger";
  visible?: (row: any) => boolean;
  disabled?: (row: any) => boolean;
}

interface DataTableProps<T> {
  data: T[];
  columns: Column<T>[];
  keyField: keyof T;
  actions?: Action<T>[];

  // Table Configuration
  pageSize?: number;
  sortable?: boolean;
  filterable?: boolean;
  searchable?: boolean;
  selectable?: boolean;

  // Master-Detail Configuration
  expandedRowRender?: (row: T) => React.ReactNode;
  expandable?: boolean;
  expandedRowKeys?: string[];
  onExpandedRowsChange?: (expandedKeys: string[]) => void;
  expandRowByClick?: boolean;
  expandIconColumnIndex?: number;
  expandIcon?: (expanded: boolean) => React.ReactNode;
  defaultExpandAllRows?: boolean;
  expandedRowClassName?: string;

  // Export
  exportable?: boolean;
  exportFormats?: ("xlsx" | "csv" | "pdf")[];
  exportFileNameKey?: string;
  onExport?: (format: string, data: T[]) => void;

  // Status Indicators
  rowStatusField?: string;
  statusConfig?: {
    [key: string]: {
      color: string;
      icon?: React.ReactNode;
      highlight?: boolean;
    };
  };

  // States
  loading?: boolean;
  error?: string;
  emptyMessageKey?: string;

  // Styling
  className?: string;
  size?: "sm" | "md" | "lg";
  striped?: boolean;
  bordered?: boolean;
  stickyHeader?: boolean;
}

// Main Component
export function DataTable<T extends object>({
  data,
  columns,
  keyField,
  actions,
  expandable = false,
  expandedRowRender,
  rowStatusField,
  statusConfig,
  stickyHeader = false,
  expandRowByClick = false,
  exportable,
  exportFormats,
  exportFileNameKey,
}: DataTableProps<T>) {
  const [expandedKey, setExpandedKey] = useState<string | null>(null);
  const hasActions = actions && actions.length > 0;

  const toggleRow = (key: string) => {
    setExpandedKey((prev) => (prev === key ? null : key));
  };

  // Export logic
  const exportFile = (format: "xlsx" | "csv" | "pdf") => {
    if (!exportFileNameKey) return;

    if (format === "pdf") {
      const doc = new jsPDF();

      if (data.length === 0) {
        doc.text("No data available", 10, 10);
      } else {
        const columnsKeys = Object.keys(data[0]);
        const rows = data.map((row) =>
          columnsKeys.map((key) => (row as any)[key])
        );

        autoTable(doc, {
          head: [columnsKeys],
          body: rows,
          startY: 20,
        });
      }

      doc.save(`${exportFileNameKey}.pdf`);
      return;
    }

    const worksheet = XLSX.utils.json_to_sheet(data);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");

    const wbout =
      format === "csv"
        ? XLSX.write(workbook, { bookType: "csv", type: "array" })
        : XLSX.write(workbook, { bookType: "xlsx", type: "array" });

    const blob = new Blob([wbout], {
      type:
        format === "csv"
          ? "text/csv;charset=utf-8;"
          : "application/octet-stream",
    });

    saveAs(blob, `${exportFileNameKey}.${format}`);
  };

  return (
    <div className="p-6 mt-30">
      {/* Export Buttons */}
      {exportable && exportFormats?.length && exportFileNameKey && (
        <div className="flex gap-2 py-4">
          {exportFormats.map((format) => (
            <button
              key={format}
              className="border px-4 py-1 rounded bg-white text-sm hover:bg-gray-100"
              onClick={() => exportFile(format)}
            >
              Export as {format.toUpperCase()}
            </button>
          ))}
        </div>
      )}

      {/* Table */}
      <div className="w-full overflow-y-auto shadow-sm bg-white">
        <Table className="w-full border-collapse text-sm">
          <TableHeader>
            <TableRow className="bg-white text-muted-foreground">
              {expandable && (
                <TableHead
                  className={cn(
                    "px-4 py-2 text-black",
                    stickyHeader && "sticky top-0 bg-white z-10 shadow-sm"
                  )}
                >
                  Details
                </TableHead>
              )}

              {columns.map((col) => (
                <TableHead
                  key={col.key}
                  style={{ width: col.width }}
                  className={cn(
                    "px-4 py-2 text-blue-500",
                    col.align && `text-${col.align}`,
                    stickyHeader && "sticky top-0 bg-white z-10 shadow-sm"
                  )}
                >
                  {col.title}
                </TableHead>
              ))}

              {hasActions && (
                <TableHead
                  className={cn(
                    "text-center",
                    stickyHeader && "sticky top-0 bg-white z-10"
                  )}
                >
                  Actions
                </TableHead>
              )}
            </TableRow>
          </TableHeader>

          <TableBody>
            {data.map((row) => {
              const rowKey = String(row[keyField]);
              const isExpanded = expandedKey === rowKey;

              return (
                <>
                  {/* Row */}
                  <TableRow
                    key={rowKey}
                    className="cursor-pointer"
                    onClick={
                      expandRowByClick ? () => toggleRow(rowKey) : undefined
                    }
                  >
                    {/* Detail Cell */}
                    {expandable && (
                      <TableCell
                        className="px-4 py-2"
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleRow(rowKey);
                        }}
                      >
                        <div className="flex">
                          <AlertCircleIcon
                            fill="yellowGreen"
                            size={22}
                            className="text-white"
                          />
                        </div>
                      </TableCell>
                    )}

                    {/* Data Columns */}
                    {columns.map((col) => {
                      const value = (row as any)[col.key];

                      // Status Field
                      if (
                        rowStatusField &&
                        col.key === rowStatusField &&
                        value &&
                        statusConfig?.[value]
                      ) {
                        const status = statusConfig[value];
                        return (
                          <TableCell
                            key={col.key}
                            className={cn(
                              "px-4 py-2 text-center",
                              status.highlight && `bg-${status.color}-100`
                            )}
                          >
                            <div className="flex justify-center items-center gap-1 text-sm">
                              {status.icon}
                            </div>
                          </TableCell>
                        );
                      }

                      return (
                        <TableCell
                          key={col.key}
                          className={cn("px-4 py-2", col.align && `text-${col.align}`)}
                         
                        >
                          {col.render ? col.render(row) : value}
                        </TableCell>
                      );
                    })}

                    {/* Actions */}
                    {hasActions && (
                      <TableCell className="flex justify-center gap-2">
                        {actions
                          .filter((a) => a.visible?.(row) ?? true)
                          .map((a, idx) => (
                            <button
                              key={idx}
                              type="button"
                              onClick={(e) => {
                                e.stopPropagation();
                                a.onClick(row);
                              }}
                            >
                              {a.icon}
                            </button>
                          ))}
                      </TableCell>
                    )}
                  </TableRow>

                  {/* Expanded Row */}
                  {isExpanded && expandedRowRender && (
                    <TableRow>
                      <TableCell
                        colSpan={
                          columns.length +
                          (hasActions ? 1 : 0) +
                          (expandable ? 1 : 0)
                        }
                      >
                        <div className="border p-4 bg-muted">
                          {expandedRowRender(row)}
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </>
              );
            })}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
