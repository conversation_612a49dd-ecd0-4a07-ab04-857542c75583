"use client";

import React, { useState, useEffect, useRef } from "react";
import { CalendarIcon, ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useLanguage } from "@/contexts/LanguageContext";

// Types
type DateFormat =
  | "dd/mm/yyyy"
  | "mm/dd/yyyy"
  | "yyyy-mm-dd"
  | "dd-mm-yyyy"
  | "short"
  | "full";
type DateType = "gregorian" | "hijri";

interface HijriInfo {
  year: number;
  month: number;
  day: number;
  monthName: string;
}

interface DateTimePickerProps {
  value?: Date;
  onChange?: (date: Date) => void;
  label?: string;
  placeholder?: string;
  dateType?: DateType;
  format?: DateFormat;
  showTime?: boolean;
  timeFormat?: "12h" | "24h";
  allowTypeSwitch?: boolean;
  className?: string;
  disabled?: boolean;
}

// Hijri Date Utility Class
class HijriDate {
  // Hijri month names in Arabic
  static hijriMonthsAr = [
    "محرم",
    "صفر",
    "ربيع الأول",
    "ربيع الثاني",
    "جمادى الأولى",
    "جمادى الثانية",
    "رجب",
    "شعبان",
    "رمضان",
    "شوال",
    "ذو القعدة",
    "ذو الحجة",
  ];

  // Hijri month names in English
  static hijriMonthsEn = [
    "Muharram",
    "Safar",
    "Rabi' al-awwal",
    "Rabi' al-thani",
    "Jumada al-awwal",
    "Jumada al-thani",
    "Rajab",
    "Sha'ban",
    "Ramadan",
    "Shawwal",
    "Dhu al-Qi'dah",
    "Dhu al-Hijjah",
  ];

  // Convert Gregorian date to Hijri
  static toHijri(gregorianDate: Date): HijriInfo {
    const jd = this.gregorianToJulian(gregorianDate);
    const hijri = this.julianToHijri(jd);

    return {
      year: hijri.year,
      month: hijri.month,
      day: hijri.day,
      monthName: this.hijriMonthsAr[hijri.month - 1],
    };
  }

  // Convert Hijri date to Gregorian
  static fromHijri(
    hijriYear: number,
    hijriMonth: number,
    hijriDay: number
  ): Date {
    const jd = this.hijriToJulian(hijriYear, hijriMonth, hijriDay);
    return this.julianToGregorian(jd);
  }

  // Get number of days in Hijri month
  static getHijriMonthDays(year: number, month: number): number {
    // Simplified calculation - in reality this would use astronomical data
    // Odd months have 30 days, even months have 29 days
    // with adjustments for leap years
    const isLeapYear = this.isHijriLeapYear(year);

    if (month === 12 && isLeapYear) {
      return 30;
    }

    return month % 2 === 1 ? 30 : 29;
  }

  // Check if Hijri year is leap year (simplified)
  static isHijriLeapYear(year: number): boolean {
    // 30-year cycle with 11 leap years
    const leapYears = [2, 5, 7, 10, 13, 16, 18, 21, 24, 26, 29];
    return leapYears.includes(year % 30);
  }

  // Helper: Convert Gregorian to Julian Day Number
  private static gregorianToJulian(date: Date): number {
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();

    const a = Math.floor((14 - month) / 12);
    const y = year - a;
    const m = month + 12 * a - 3;

    return (
      day +
      Math.floor((153 * m + 2) / 5) +
      365 * y +
      Math.floor(y / 4) -
      Math.floor(y / 100) +
      Math.floor(y / 400) +
      1721119
    );
  }

  // Helper: Convert Julian Day Number to Hijri
  private static julianToHijri(jd: number): {
    year: number;
    month: number;
    day: number;
  } {
    // Simplified conversion - uses average lunar month length
    const epochJd = 1948439.5; // Hijri epoch (July 16, 622 CE)
    const daysSinceEpoch = jd - epochJd;

    // Average lunar month is approximately 29.530588 days
    const lunarMonth = 29.530588;
    const lunarYear = 12 * lunarMonth;

    let year = Math.floor(daysSinceEpoch / lunarYear) + 1;
    let remainingDays = daysSinceEpoch - (year - 1) * lunarYear;

    let month = Math.floor(remainingDays / lunarMonth) + 1;
    if (month > 12) {
      year++;
      month = 1;
      remainingDays -= 12 * lunarMonth;
    }

    let day = Math.floor(remainingDays - (month - 1) * lunarMonth) + 1;

    // Ensure valid ranges
    year = Math.max(1, year);
    month = Math.max(1, Math.min(12, month));
    day = Math.max(1, Math.min(this.getHijriMonthDays(year, month), day));

    return { year, month, day };
  }

  // Helper: Convert Hijri to Julian Day Number
  private static hijriToJulian(
    year: number,
    month: number,
    day: number
  ): number {
    const epochJd = 1948439.5;
    const lunarMonth = 29.530588;
    const lunarYear = 12 * lunarMonth;

    const totalDays =
      (year - 1) * lunarYear + (month - 1) * lunarMonth + (day - 1);
    return epochJd + totalDays;
  }

  // Helper: Convert Julian Day Number to Gregorian
  private static julianToGregorian(jd: number): Date {
    const a = jd + 32044;
    const b = Math.floor((4 * a + 3) / 146097);
    const c = a - Math.floor((146097 * b) / 4);
    const d = Math.floor((4 * c + 3) / 1461);
    const e = c - Math.floor((1461 * d) / 4);
    const m = Math.floor((5 * e + 2) / 153);

    const day = e - Math.floor((153 * m + 2) / 5) + 1;
    const month = m + 3 - 12 * Math.floor(m / 10);
    const year = 100 * b + d - 4800 + Math.floor(m / 10);

    return new Date(year, month - 1, day);
  }
}

// Date formatting utility
function formatDateByType(
  date: Date | undefined,
  dateType: DateType,
  format: DateFormat,
  language: string,
  showTime: boolean = false,
  timeFormat: "12h" | "24h" = "24h",
  hijriInfo?: HijriInfo | null
): string {
  if (!date) return "";

  const formatTime = (date: Date): string => {
    if (timeFormat === "12h") {
      return date.toLocaleTimeString(language === "ar" ? "ar-SA" : "en-US", {
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      });
    } else {
      return `${date.getHours().toString().padStart(2, "0")}:${date
        .getMinutes()
        .toString()
        .padStart(2, "0")}`;
    }
  };

  let dateStr = "";

  if (dateType === "hijri") {
    const hijri = hijriInfo || HijriDate.toHijri(date);

    switch (format) {
      case "full":
        dateStr =
          language === "ar"
            ? `${hijri.day} ${hijri.monthName} ${hijri.year} هـ`
            : `${hijri.day} ${HijriDate.hijriMonthsEn[hijri.month - 1]} ${
                hijri.year
              } AH`;
        break;
      case "short":
        dateStr =
          language === "ar"
            ? `${hijri.day}/${hijri.month}/${hijri.year} هـ`
            : `${hijri.day}/${hijri.month}/${hijri.year} AH`;
        break;
      case "dd/mm/yyyy":
        dateStr = `${hijri.day.toString().padStart(2, "0")}/${hijri.month
          .toString()
          .padStart(2, "0")}/${hijri.year}`;
        break;
      case "mm/dd/yyyy":
        dateStr = `${hijri.month.toString().padStart(2, "0")}/${hijri.day
          .toString()
          .padStart(2, "0")}/${hijri.year}`;
        break;
      case "yyyy-mm-dd":
        dateStr = `${hijri.year}-${hijri.month
          .toString()
          .padStart(2, "0")}-${hijri.day.toString().padStart(2, "0")}`;
        break;
      case "dd-mm-yyyy":
        dateStr = `${hijri.day.toString().padStart(2, "0")}-${hijri.month
          .toString()
          .padStart(2, "0")}-${hijri.year}`;
        break;
    }
  } else {
    // Gregorian formatting
    const day = date.getDate();
    const month = date.getMonth() + 1;
    const year = date.getFullYear();

    switch (format) {
      case "full":
        dateStr = date.toLocaleDateString(
          language === "ar" ? "ar-SA" : "en-US",
          {
            year: "numeric",
            month: "long",
            day: "numeric",
          }
        );
        break;
      case "short":
        dateStr = date.toLocaleDateString(
          language === "ar" ? "ar-SA" : "en-US",
          {
            year: "numeric",
            month: "short",
            day: "numeric",
          }
        );
        break;
      case "dd/mm/yyyy":
        dateStr = `${day.toString().padStart(2, "0")}/${month
          .toString()
          .padStart(2, "0")}/${year}`;
        break;
      case "mm/dd/yyyy":
        dateStr = `${month.toString().padStart(2, "0")}/${day
          .toString()
          .padStart(2, "0")}/${year}`;
        break;
      case "yyyy-mm-dd":
        dateStr = `${year}-${month.toString().padStart(2, "0")}-${day
          .toString()
          .padStart(2, "0")}`;
        break;
      case "dd-mm-yyyy":
        dateStr = `${day.toString().padStart(2, "0")}-${month
          .toString()
          .padStart(2, "0")}-${year}`;
        break;
    }
  }

  return showTime ? `${dateStr} ${formatTime(date)}` : dateStr;
}

// Input date parsing utility
function parseInputDate(input: string): Date | null {
  if (!input.trim()) return null;

  // Try different date formats
  const formats = [
    // ISO format: 2024-01-15
    /^(\d{4})-(\d{1,2})-(\d{1,2})$/,
    // DD/MM/YYYY: 15/01/2024
    /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/,
    // MM/DD/YYYY: 01/15/2024
    /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/,
    // DD-MM-YYYY: 15-01-2024
    /^(\d{1,2})-(\d{1,2})-(\d{4})$/,
    // YYYY/MM/DD: 2024/01/15
    /^(\d{4})\/(\d{1,2})\/(\d{1,2})$/,
  ];

  for (let i = 0; i < formats.length; i++) {
    const match = input.match(formats[i]);
    if (match) {
      let year, month, day;

      if (i === 0 || i === 4) {
        // ISO or YYYY/MM/DD
        year = parseInt(match[1]);
        month = parseInt(match[2]) - 1; // JavaScript months are 0-based
        day = parseInt(match[3]);
      } else if (i === 1 || i === 3) {
        // DD/MM/YYYY or DD-MM-YYYY
        day = parseInt(match[1]);
        month = parseInt(match[2]) - 1;
        year = parseInt(match[3]);
      } else {
        // MM/DD/YYYY
        month = parseInt(match[1]) - 1;
        day = parseInt(match[2]);
        year = parseInt(match[3]);
      }

      const date = new Date(year, month, day);
      // Validate the date
      if (
        date.getFullYear() === year &&
        date.getMonth() === month &&
        date.getDate() === day
      ) {
        return date;
      }
    }
  }

  return null;
}

// Main DateTimePicker Component
export default function DateTimePicker({
  value,
  onChange,
  label,
  placeholder,
  dateType = "hijri",
  format = "full",
  showTime = false,
  timeFormat = "24h",
  allowTypeSwitch = false,
  className = "",
  disabled = false,
}: DateTimePickerProps) {
  const { language } = useLanguage();
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(value);
  const [selectedHijriDate, setSelectedHijriDate] = useState<HijriInfo | null>(
    null
  );
  const [currentDateType, setCurrentDateType] = useState<DateType>(dateType);
  const [isOpen, setIsOpen] = useState(false);
  const [inputValue, setInputValue] = useState("");
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [selectedTime, setSelectedTime] = useState({ hours: 0, minutes: 0 });

  const containerRef = useRef<HTMLDivElement>(null);

  // Initialize with today's date if no value provided
  useEffect(() => {
    const today = new Date();
    if (!value) {
      setSelectedDate(today);
      if (currentDateType === "hijri") {
        setSelectedHijriDate(HijriDate.toHijri(today));
      }
    } else {
      setSelectedDate(value);
      if (currentDateType === "hijri") {
        setSelectedHijriDate(HijriDate.toHijri(value));
      }
    }
  }, [value, currentDateType]);

  // Update input value when date changes
  useEffect(() => {
    if (selectedDate) {
      const formatted = formatDateByType(
        selectedDate,
        currentDateType,
        format,
        language,
        showTime,
        timeFormat,
        selectedHijriDate
      );
      setInputValue(formatted);
    } else {
      setInputValue("");
    }
  }, [
    selectedDate,
    currentDateType,
    format,
    language,
    showTime,
    timeFormat,
    selectedHijriDate,
  ]);

  // Handle outside click to close calendar
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    }

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      return () =>
        document.removeEventListener("mousedown", handleClickOutside);
    }
  }, [isOpen]);

  const handleDateSelect = (date: Date, hijriInfo?: HijriInfo) => {
    const newDate = new Date(date);

    if (showTime) {
      newDate.setHours(selectedTime.hours, selectedTime.minutes);
    }

    setSelectedDate(newDate);

    if (hijriInfo) {
      setSelectedHijriDate(hijriInfo);
    } else if (currentDateType === "hijri") {
      setSelectedHijriDate(HijriDate.toHijri(newDate));
    }

    onChange?.(newDate);

    if (!showTime) {
      setIsOpen(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);

    const parsedDate = parseInputDate(value);
    if (parsedDate) {
      handleDateSelect(parsedDate);
    }
  };

  const handleTimeChange = (hours: number, minutes: number) => {
    setSelectedTime({ hours, minutes });

    if (selectedDate) {
      const newDate = new Date(selectedDate);
      newDate.setHours(hours, minutes);
      setSelectedDate(newDate);
      onChange?.(newDate);
    }
  };

  const renderCalendar = () => {
    if (currentDateType === "hijri") {
      return renderHijriCalendar();
    } else {
      return renderGregorianCalendar();
    }
  };

  const renderGregorianCalendar = () => {
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();

    const days = [];
    const today = new Date();

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(<div key={`empty-${i}`} className="w-8 h-8" />);
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month, day);
      const isToday = date.toDateString() === today.toDateString();
      const isSelected =
        selectedDate && date.toDateString() === selectedDate.toDateString();

      days.push(
        <button
          key={day}
          onClick={() => handleDateSelect(date)}
          className={cn(
            "w-8 h-8 text-sm rounded-md hover:bg-blue-100 transition-colors",
            isToday && "border border-blue-500 font-semibold",
            isSelected && "bg-blue-500 text-white hover:bg-blue-600",
            !isSelected && !isToday && "hover:bg-gray-100"
          )}
        >
          {day}
        </button>
      );
    }

    return (
      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setCurrentMonth(new Date(year, month - 1, 1))}
          >
            <ChevronLeft className="w-4 h-4" />
          </Button>
          <span className="font-medium">
            {currentMonth.toLocaleDateString(
              language === "ar" ? "ar-SA" : "en-US",
              {
                year: "numeric",
                month: "long",
              }
            )}
          </span>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setCurrentMonth(new Date(year, month + 1, 1))}
          >
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>

        {/* Day headers */}
        <div className="grid grid-cols-7 gap-1 mb-2">
          {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map((day) => (
            <div
              key={day}
              className="w-8 h-8 text-xs font-medium text-gray-500 flex items-center justify-center"
            >
              {language === "ar"
                ? ["أحد", "اثنين", "ثلاثاء", "أربعاء", "خميس", "جمعة", "سبت"][
                    ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].indexOf(
                      day
                    )
                  ]
                : day}
            </div>
          ))}
        </div>

        {/* Calendar grid */}
        <div className="grid grid-cols-7 gap-1">{days}</div>
      </div>
    );
  };

  const renderHijriCalendar = () => {
    const currentHijri = HijriDate.toHijri(currentMonth);
    const hijriMonths =
      language === "ar" ? HijriDate.hijriMonthsAr : HijriDate.hijriMonthsEn;

    // Get the first day of the current Hijri month in Gregorian
    const firstDayGregorian = HijriDate.fromHijri(
      currentHijri.year,
      currentHijri.month,
      1
    );
    const daysInMonth = HijriDate.getHijriMonthDays(
      currentHijri.year,
      currentHijri.month
    );
    const startingDayOfWeek = firstDayGregorian.getDay();

    const days = [];
    const today = new Date();
    const todayHijri = HijriDate.toHijri(today);

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(<div key={`empty-${i}`} className="w-8 h-8" />);
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const gregorianDate = HijriDate.fromHijri(
        currentHijri.year,
        currentHijri.month,
        day
      );
      const hijriInfo: HijriInfo = {
        year: currentHijri.year,
        month: currentHijri.month,
        day: day,
        monthName: hijriMonths[currentHijri.month - 1],
      };

      const isToday =
        todayHijri.year === currentHijri.year &&
        todayHijri.month === currentHijri.month &&
        todayHijri.day === day;

      const isSelected =
        selectedHijriDate &&
        selectedHijriDate.year === currentHijri.year &&
        selectedHijriDate.month === currentHijri.month &&
        selectedHijriDate.day === day;

      days.push(
        <button
          key={day}
          onClick={() => handleDateSelect(gregorianDate, hijriInfo)}
          className={cn(
            "w-8 h-8 text-sm rounded-md hover:bg-blue-100 transition-colors",
            isToday && "border border-blue-500 font-semibold",
            isSelected && "bg-blue-500 text-white hover:bg-blue-600",
            !isSelected && !isToday && "hover:bg-gray-100"
          )}
        >
          {day}
        </button>
      );
    }

    return (
      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              const prevMonth =
                currentHijri.month === 1 ? 12 : currentHijri.month - 1;
              const prevYear =
                currentHijri.month === 1
                  ? currentHijri.year - 1
                  : currentHijri.year;
              const prevMonthGregorian = HijriDate.fromHijri(
                prevYear,
                prevMonth,
                1
              );
              setCurrentMonth(prevMonthGregorian);
            }}
          >
            <ChevronLeft className="w-4 h-4" />
          </Button>
          <span className="font-medium">
            {hijriMonths[currentHijri.month - 1]} {currentHijri.year}
          </span>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              const nextMonth =
                currentHijri.month === 12 ? 1 : currentHijri.month + 1;
              const nextYear =
                currentHijri.month === 12
                  ? currentHijri.year + 1
                  : currentHijri.year;
              const nextMonthGregorian = HijriDate.fromHijri(
                nextYear,
                nextMonth,
                1
              );
              setCurrentMonth(nextMonthGregorian);
            }}
          >
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>

        {/* Day headers */}
        <div className="grid grid-cols-7 gap-1 mb-2">
          {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map((day) => (
            <div
              key={day}
              className="w-8 h-8 text-xs font-medium text-gray-500 flex items-center justify-center"
            >
              {language === "ar"
                ? ["أحد", "اثنين", "ثلاثاء", "أربعاء", "خميس", "جمعة", "سبت"][
                    ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].indexOf(
                      day
                    )
                  ]
                : day}
            </div>
          ))}
        </div>

        {/* Calendar grid */}
        <div className="grid grid-cols-7 gap-1">{days}</div>
      </div>
    );
  };

  const renderTimePicker = () => {
    if (!showTime) return null;

    return (
      <div className="p-4 border-t bg-gray-50">
        <div className="flex items-center justify-center space-x-4">
          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium text-gray-700">
              {language === "ar" ? "الساعة" : "Hour"}
            </label>
            <select
              value={selectedTime.hours}
              onChange={(e) =>
                handleTimeChange(parseInt(e.target.value), selectedTime.minutes)
              }
              className="px-2 py-1 border border-gray-300 rounded text-sm"
            >
              {Array.from({ length: 24 }, (_, i) => (
                <option key={i} value={i}>
                  {timeFormat === "12h"
                    ? (i === 0 ? 12 : i > 12 ? i - 12 : i)
                        .toString()
                        .padStart(2, "0")
                    : i.toString().padStart(2, "0")}
                </option>
              ))}
            </select>
            {timeFormat === "12h" && (
              <select
                value={selectedTime.hours >= 12 ? "PM" : "AM"}
                onChange={(e) => {
                  const isPM = e.target.value === "PM";
                  let newHours = selectedTime.hours;
                  if (isPM && selectedTime.hours < 12) {
                    newHours += 12;
                  } else if (!isPM && selectedTime.hours >= 12) {
                    newHours -= 12;
                  }
                  handleTimeChange(newHours, selectedTime.minutes);
                }}
                className="px-2 py-1 border border-gray-300 rounded text-sm"
              >
                <option value="AM">AM</option>
                <option value="PM">PM</option>
              </select>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <label className="text-sm font-medium text-gray-700">
              {language === "ar" ? "الدقيقة" : "Minute"}
            </label>
            <select
              value={selectedTime.minutes}
              onChange={(e) =>
                handleTimeChange(selectedTime.hours, parseInt(e.target.value))
              }
              className="px-2 py-1 border border-gray-300 rounded text-sm"
            >
              {Array.from({ length: 60 }, (_, i) => (
                <option key={i} value={i}>
                  {i.toString().padStart(2, "0")}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className={cn("flex flex-col gap-2", className)} ref={containerRef}>
      {label && (
        <label className="text-sm font-medium text-gray-700">{label}</label>
      )}

      <div className="relative">
        <input
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onClick={() => !disabled && setIsOpen(true)}
          placeholder={
            placeholder || (language === "ar" ? "اختر التاريخ" : "Select date")
          }
          disabled={disabled}
          className={cn(
            "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",
            disabled && "bg-gray-100 cursor-not-allowed",
            language === "ar" && "text-right"
          )}
        />

        <button
          type="button"
          onClick={() => !disabled && setIsOpen(!isOpen)}
          disabled={disabled}
          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
        >
          <CalendarIcon className="w-4 h-4" />
        </button>
      </div>

      {isOpen && (
        <div className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[300px]">
          {/* Calendar type switcher */}
          {allowTypeSwitch && (
            <div className="p-3 border-b bg-gray-50">
              <div className="flex items-center justify-center space-x-2">
                <Button
                  variant={
                    currentDateType === "gregorian" ? "default" : "outline"
                  }
                  size="sm"
                  onClick={() => setCurrentDateType("gregorian")}
                >
                  {language === "ar" ? "ميلادي" : "Gregorian"}
                </Button>
                <Button
                  variant={currentDateType === "hijri" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setCurrentDateType("hijri")}
                >
                  {language === "ar" ? "هجري" : "Hijri"}
                </Button>
              </div>
            </div>
          )}

          {/* Calendar */}
          {renderCalendar()}

          {/* Time picker */}
          {renderTimePicker()}

          {/* Action buttons */}
          <div className="p-3 border-t bg-gray-50 flex justify-end space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsOpen(false)}
            >
              {language === "ar" ? "إلغاء" : "Cancel"}
            </Button>
            <Button
              size="sm"
              onClick={() => {
                if (selectedDate) {
                  onChange?.(selectedDate);
                }
                setIsOpen(false);
              }}
            >
              {language === "ar" ? "تأكيد" : "Confirm"}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
